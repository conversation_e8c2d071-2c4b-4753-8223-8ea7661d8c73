import { RemoteComponent } from './RemoteComponent.mjs';
import { RemoteText } from './RemoteText.mjs';
import { jsx } from 'react/jsx-runtime';

function createController(components, {
  renderComponent: externalRenderComponent,
  renderText: externalRenderText
} = {}) {
  const registry = new Map(Object.entries(components));

  const defaultRenderComponent = ({
    parent,
    component,
    controller,
    receiver,
    key
  }) => {
    return /*#__PURE__*/jsx(RemoteComponent, {
      parent: parent,
      component: component,
      controller: controller,
      receiver: receiver
    }, key);
  };

  const renderComponent = externalRenderComponent ? componentProps => externalRenderComponent(componentProps, {
    renderDefault() {
      return defaultRenderComponent(componentProps);
    }

  }) : defaultRenderComponent;

  const defaultRenderText = ({
    key,
    receiver,
    text,
    parent
  }) => {
    return /*#__PURE__*/jsx(RemoteText, {
      receiver: receiver,
      text: text,
      parent: parent
    }, key);
  };

  const renderText = externalRenderText ? textProps => externalRenderText(textProps, {
    renderDefault() {
      return defaultRenderText(textProps);
    }

  }) : defaultRenderText;
  return {
    get(type) {
      const value = registry.get(type);

      if (value == null) {
        throw new Error(`Unknown component: ${type}`);
      }

      return value;
    },

    renderer: {
      renderComponent,
      renderText
    }
  };
}

export { createController };
