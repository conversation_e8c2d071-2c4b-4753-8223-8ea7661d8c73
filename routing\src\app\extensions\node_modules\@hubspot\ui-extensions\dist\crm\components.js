import { createExtensionComponent } from '../utils/createExtensionComponent';
const CrmPropertyList = createExtensionComponent('CrmPropertyList');
const CrmAssociationTable = createExtensionComponent('CrmAssociationTable');
const CrmDataHighlight = createExtensionComponent('CrmDataHighlight');
const CrmReport = createExtensionComponent('CrmReport');
const CrmAssociationPivot = createExtensionComponent('CrmAssociationPivot');
const CrmAssociationPropertyList = createExtensionComponent('CrmAssociationPropertyList');
const CrmAssociationStageTracker = createExtensionComponent('CrmAssociationStageTracker');
const CrmSimpleDeadline = createExtensionComponent('CrmSimpleDeadline');
const CrmStageTracker = createExtensionComponent('CrmStageTracker');
const CrmStatistics = createExtensionComponent('CrmStatistics');
const CrmActionButton = createExtensionComponent('CrmActionButton');
const CrmActionLink = createExtensionComponent('CrmActionLink');
const CrmCardActions = createExtensionComponent('CrmCardActions');
export { CrmPropertyList, CrmAssociationTable, CrmDataHighlight, CrmReport, CrmAssociationPivot, CrmAssociationPropertyList, CrmAssociationStageTracker, CrmSimpleDeadline, CrmStageTracker, CrmStatistics, CrmActionButton, CrmActionLink, CrmCardActions, };
