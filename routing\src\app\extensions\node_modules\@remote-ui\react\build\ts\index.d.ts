export { createRemoteRoot } from '@remote-ui/core';
export type { RemoteRoot, RemoteReceiver } from '@remote-ui/core';
export { render, createRoot } from './render';
export type { Root } from './render';
export { createRemoteReactComponent } from './components';
export { useRemoteSubscription } from './hooks';
export type { ReactPropsFromRemoteComponentType, ReactComponentTypeFromRemoteComponentType, } from './types';
export { createReconciler } from './reconciler';
export type { Reconciler } from './reconciler';
//# sourceMappingURL=index.d.ts.map