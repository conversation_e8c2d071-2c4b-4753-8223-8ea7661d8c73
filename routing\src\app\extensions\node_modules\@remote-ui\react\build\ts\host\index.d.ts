export type { RemoteReceiver } from '@remote-ui/core';
export { createRemoteReceiver } from '@remote-ui/core';
export { RemoteRenderer } from './RemoteRenderer';
export type { RemoteRendererProps } from './RemoteRenderer';
export { RemoteComponent } from './RemoteComponent';
export { RemoteText } from './RemoteText';
export { createController } from './controller';
export { useAttached } from './hooks';
export type { ComponentMapping, ControllerOptions } from './controller';
export type { ReactPropsFromRemoteComponentType, ReactComponentTypeFromRemoteComponentType, } from '../types';
export type { Controller, RemoteComponentProps, RemoteTextProps } from './types';
//# sourceMappingURL=index.d.ts.map