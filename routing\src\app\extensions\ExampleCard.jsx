import React, { useState } from "react";
import {
  Text,
  Input,
  Select,
  TextArea,
  Button,
  <PERSON>lex,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  LoadingSpinner
} from "@hubspot/ui-extensions";
import { hubspot } from "@hubspot/ui-extensions";

hubspot.extend(({ context, actions }) => <RouteManagementExtension context={context} actions={actions} />);

const RouteManagementExtension = ({ context, actions }) => {
  // State for custom fields
  const [margin, setMargin] = useState("");
  const [mode, setMode] = useState("");
  const [tariff, setTariff] = useState("");
  const [price, setPrice] = useState("");
  const [routeSearch, setRouteSearch] = useState("");
  const [selectedRoute, setSelectedRoute] = useState("");
  const [routeDetails, setRouteDetails] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Get deal ID from context
  const dealId = context.crm.objectId;

  // Route options
  const routeOptions = [
    { label: "Select a route", value: "" },
    { label: "Route 1", value: "route1" },
    { label: "Route 2", value: "route2" },
    { label: "Route 3", value: "route3" },
    { label: "Route 4", value: "route4" },
    { label: "Route 5", value: "route5" }
  ];

  // Route details mapping
  const routeDetailsMap = {
    route1: "Route 1: Express delivery via Highway A - Estimated time: 2-3 hours",
    route2: "Route 2: Standard delivery via Main Road - Estimated time: 4-5 hours",
    route3: "Route 3: Economy route via Secondary Roads - Estimated time: 6-8 hours",
    route4: "Route 4: Premium route with tracking - Estimated time: 1-2 hours",
    route5: "Route 5: Overnight delivery - Estimated time: 12-24 hours"
  };

  // Handle route search change
  const handleRouteSearchChange = (value) => {
    setRouteSearch(value);
    if (value === "no") {
      setSelectedRoute("");
      setRouteDetails("");
    }
  };

  // Handle route selection
  const handleRouteSelection = (value) => {
    setSelectedRoute(value);
    if (value && routeDetailsMap[value]) {
      setRouteDetails(routeDetailsMap[value]);
    } else {
      setRouteDetails("");
    }
  };

  // Save deal properties
  const saveDealProperties = async () => {
    setLoading(true);
    setMessage("");

    try {
      // Prepare properties to update
      const properties = {
        margin: margin,
        mode: mode,
        tariff: tariff,
        price: price,
        route_search: routeSearch
      };

      // Add route-specific properties if route search is "yes"
      if (routeSearch === "yes") {
        properties.route_selector = selectedRoute;
        properties.selected_route = routeDetails;
      }

      // Call server endpoint to update deal properties
      const response = await fetch(`http://localhost:3000/update-deal/${dealId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ properties })
      });

      if (response.ok) {
        setMessage("Deal properties updated successfully!");
        // Refresh the CRM record to show updated properties
        actions.refreshObjectProperties();
      } else {
        setMessage("Error updating deal properties");
      }
    } catch (error) {
      console.error("Error:", error);
      setMessage("Error updating deal properties");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex direction="column" gap="medium">
      <Text variant="microcopy">
        Deal ID: {dealId}
      </Text>

      <Divider />

      <Text format={{ fontWeight: "bold" }}>
        Deal Custom Fields
      </Text>

      {/* Margin Field */}
      <Flex direction="column" gap="small">
        <Text>Margin</Text>
        <Input
          name="margin"
          value={margin}
          onChange={setMargin}
          placeholder="Enter margin value"
        />
      </Flex>

      {/* Mode Field */}
      <Flex direction="column" gap="small">
        <Text>Mode</Text>
        <Input
          name="mode"
          value={mode}
          onChange={setMode}
          placeholder="Enter mode"
        />
      </Flex>

      {/* Tariff Field */}
      <Flex direction="column" gap="small">
        <Text>Tariff</Text>
        <Input
          name="tariff"
          value={tariff}
          onChange={setTariff}
          placeholder="Enter tariff"
        />
      </Flex>

      {/* Price Field */}
      <Flex direction="column" gap="small">
        <Text>Price</Text>
        <Input
          name="price"
          value={price}
          onChange={setPrice}
          placeholder="Enter price"
        />
      </Flex>

      {/* Route Search Field */}
      <Flex direction="column" gap="small">
        <Text>Route Search</Text>
        <Select
          name="routeSearch"
          value={routeSearch}
          onChange={handleRouteSearchChange}
          options={[
            { label: "Select option", value: "" },
            { label: "Yes", value: "yes" },
            { label: "No", value: "no" }
          ]}
        />
      </Flex>

      {/* Conditional Route Selector - Only show if Route Search is "Yes" */}
      {routeSearch === "yes" && (
        <>
          <Flex direction="column" gap="small">
            <Text>Route Selector</Text>
            <Select
              name="routeSelector"
              value={selectedRoute}
              onChange={handleRouteSelection}
              options={routeOptions}
            />
          </Flex>

          {/* Route Details TextArea - Only show if a route is selected */}
          {selectedRoute && (
            <Flex direction="column" gap="small">
              <Text>Selected Route Details</Text>
              <TextArea
                name="routeDetails"
                value={routeDetails}
                onChange={setRouteDetails}
                rows={3}
                readOnly={false}
              />
            </Flex>
          )}
        </>
      )}

      <Divider />

      {/* Save Button */}
      <Button
        type="submit"
        onClick={saveDealProperties}
        disabled={loading}
      >
        {loading ? <LoadingSpinner /> : "Save Deal Properties"}
      </Button>

      {/* Status Message */}
      {message && (
        <Alert title={message} variant={message.includes("Error") ? "error" : "success"} />
      )}
    </Flex>
  );
};
