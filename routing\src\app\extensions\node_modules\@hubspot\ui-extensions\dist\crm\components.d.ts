import { CrmAssociationPivotProps, CrmAssociationTableProps, CrmDataHighlightProps, CrmPropertyListProps, CrmReportProps, CrmAssociationPropertyListProps, CrmAssociationStageTrackerProps, CrmSimpleDeadlineProps, CrmStageTrackerProps, CrmStatisticsProps, CrmActionButtonProps, CrmActionLinkProps, CrmCardActionsProps } from '../types';
declare const CrmPropertyList: "CrmPropertyList" & {
    readonly type?: "CrmPropertyList" | undefined;
    readonly props?: CrmPropertyListProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmPropertyList", CrmPropertyListProps, true>>;
declare const CrmAssociationTable: "CrmAssociationTable" & {
    readonly type?: "CrmAssociationTable" | undefined;
    readonly props?: CrmAssociationTableProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmAssociationTable", CrmAssociationTableProps, true>>;
declare const CrmDataHighlight: "CrmDataHighlight" & {
    readonly type?: "CrmDataHighlight" | undefined;
    readonly props?: CrmDataHighlightProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmDataHighlight", CrmDataHighlightProps, true>>;
declare const CrmReport: "CrmReport" & {
    readonly type?: "CrmReport" | undefined;
    readonly props?: CrmReportProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmReport", CrmReportProps, true>>;
declare const CrmAssociationPivot: "CrmAssociationPivot" & {
    readonly type?: "CrmAssociationPivot" | undefined;
    readonly props?: CrmAssociationPivotProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmAssociationPivot", CrmAssociationPivotProps, true>>;
declare const CrmAssociationPropertyList: "CrmAssociationPropertyList" & {
    readonly type?: "CrmAssociationPropertyList" | undefined;
    readonly props?: CrmAssociationPropertyListProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmAssociationPropertyList", CrmAssociationPropertyListProps, true>>;
declare const CrmAssociationStageTracker: "CrmAssociationStageTracker" & {
    readonly type?: "CrmAssociationStageTracker" | undefined;
    readonly props?: CrmAssociationStageTrackerProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmAssociationStageTracker", CrmAssociationStageTrackerProps, true>>;
declare const CrmSimpleDeadline: "CrmSimpleDeadline" & {
    readonly type?: "CrmSimpleDeadline" | undefined;
    readonly props?: CrmSimpleDeadlineProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmSimpleDeadline", CrmSimpleDeadlineProps, true>>;
declare const CrmStageTracker: "CrmStageTracker" & {
    readonly type?: "CrmStageTracker" | undefined;
    readonly props?: CrmStageTrackerProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmStageTracker", CrmStageTrackerProps, true>>;
declare const CrmStatistics: "CrmStatistics" & {
    readonly type?: "CrmStatistics" | undefined;
    readonly props?: CrmStatisticsProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmStatistics", CrmStatisticsProps, true>>;
declare const CrmActionButton: "CrmActionButton" & {
    readonly type?: "CrmActionButton" | undefined;
    readonly props?: CrmActionButtonProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmActionButton", CrmActionButtonProps, true>>;
declare const CrmActionLink: "CrmActionLink" & {
    readonly type?: "CrmActionLink" | undefined;
    readonly props?: CrmActionLinkProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmActionLink", CrmActionLinkProps, true>>;
declare const CrmCardActions: "CrmCardActions" & {
    readonly type?: "CrmCardActions" | undefined;
    readonly props?: CrmCardActionsProps | undefined;
    readonly children?: true | undefined;
} & import("@remote-ui/react").ReactComponentTypeFromRemoteComponentType<import("@remote-ui/types").RemoteComponentType<"CrmCardActions", CrmCardActionsProps, true>>;
export { CrmPropertyList, CrmAssociationTable, CrmDataHighlight, CrmReport, CrmAssociationPivot, CrmAssociationPropertyList, CrmAssociationStageTracker, CrmSimpleDeadline, CrmStageTracker, CrmStatistics, CrmActionButton, CrmActionLink, CrmCardActions, };
