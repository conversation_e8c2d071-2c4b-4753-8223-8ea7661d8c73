{"name": "@remote-ui/react", "version": "5.0.2", "publishConfig": {"access": "public", "@remote-ui:registry": "https://registry.npmjs.org"}, "license": "MIT", "main": "index.js", "module": "index.mjs", "esnext": "index.esnext", "types": "./build/ts/index.d.ts", "typesVersions": {"*": {"host": ["./build/ts/host/index.d.ts"]}}, "exports": {".": {"types": "./build/ts/index.d.ts", "esnext": "./index.esnext", "import": "./index.mjs", "require": "./index.js"}, "./host": {"types": "./build/ts/host/index.d.ts", "esnext": "./host.esnext", "import": "./host.mjs", "require": "./host.js"}}, "sideEffects": false, "devDependencies": {"@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-reconciler": "^0.29.0"}, "dependencies": {"@remote-ui/async-subscription": "^2.1.14", "@remote-ui/core": "^2.2.2", "@remote-ui/rpc": "^1.4.3", "@types/react": ">=17.0.0 <19.0.0", "@types/react-reconciler": ">=0.26.0 <0.30.0"}, "peerDependencies": {"react": ">=17.0.0 <19.0.0", "react-reconciler": ">=0.26.0 <0.30.0"}, "peerDependenciesMeta": {"react": {"optional": false}, "react-reconciler": {"optional": true}}}