import { RETAINED_BY, RELEASE_METHOD, RETAIN_METHOD } from '../types.mjs';
import { StackFrame, isMemoryManageable, isBasicObject } from '../memory.mjs';

const FUNCTION = '_@f';
function createBasicEncoder(api) {
  const functionsToId = new Map();
  const idsToFunction = new Map();
  const idsToProxy = new Map();
  return {
    encode,
    decode,

    async call(id, args) {
      const stackFrame = new StackFrame();
      const func = idsToFunction.get(id);

      if (func == null) {
        throw new Error('You attempted to call a function that was already released.');
      }

      try {
        const retainedBy = isMemoryManageable(func) ? [stackFrame, ...func[RETAINED_BY]] : [stackFrame];
        const result = await func(...decode(args, retainedBy));
        return result;
      } finally {
        stackFrame.release();
      }
    },

    release(id) {
      const func = idsToFunction.get(id);

      if (func) {
        idsToFunction.delete(id);
        functionsToId.delete(func);
      }
    },

    terminate() {
      functionsToId.clear();
      idsToFunction.clear();
      idsToProxy.clear();
    }

  };

  function encode(value, seen = new Map()) {
    if (value == null) {
      return [value];
    }

    const seenValue = seen.get(value);

    if (seenValue) {
      return seenValue;
    }

    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        seen.set(value, [undefined]);
        const transferables = [];
        const result = value.map(item => {
          const [result, nestedTransferables = []] = encode(item, seen);
          transferables.push(...nestedTransferables);
          return result;
        });
        const fullResult = [result, transferables];
        seen.set(value, fullResult);
        return fullResult;
      }

      if (isBasicObject(value)) {
        seen.set(value, [undefined]);
        const transferables = [];
        const result = Object.keys(value).reduce((object, key) => {
          const [result, nestedTransferables = []] = encode(value[key], seen);
          transferables.push(...nestedTransferables);
          return { ...object,
            [key]: result
          };
        }, {});
        const fullResult = [result, transferables];
        seen.set(value, fullResult);
        return fullResult;
      }
    }

    if (typeof value === 'function') {
      if (functionsToId.has(value)) {
        const id = functionsToId.get(value);
        const result = [{
          [FUNCTION]: id
        }];
        seen.set(value, result);
        return result;
      }

      const id = api.uuid();
      functionsToId.set(value, id);
      idsToFunction.set(id, value);
      const result = [{
        [FUNCTION]: id
      }];
      seen.set(value, result);
      return result;
    }

    const result = [value];
    seen.set(value, result);
    return result;
  }

  function decode(value, retainedBy) {
    if (typeof value === 'object') {
      if (value == null) {
        return value;
      }

      if (Array.isArray(value)) {
        return value.map(value => decode(value, retainedBy));
      }

      if (FUNCTION in value) {
        const id = value[FUNCTION];

        if (idsToProxy.has(id)) {
          return idsToProxy.get(id);
        }

        let retainCount = 0;
        let released = false;

        const release = () => {
          retainCount -= 1;

          if (retainCount === 0) {
            released = true;
            idsToProxy.delete(id);
            api.release(id);
          }
        };

        const retain = () => {
          retainCount += 1;
        };

        const retainers = new Set(retainedBy);

        const proxy = (...args) => {
          if (released) {
            throw new Error('You attempted to call a function that was already released.');
          }

          if (!idsToProxy.has(id)) {
            throw new Error('You attempted to call a function that was already revoked.');
          }

          return api.call(id, args);
        };

        Object.defineProperties(proxy, {
          [RELEASE_METHOD]: {
            value: release,
            writable: false
          },
          [RETAIN_METHOD]: {
            value: retain,
            writable: false
          },
          [RETAINED_BY]: {
            value: retainers,
            writable: false
          }
        });

        for (const retainer of retainers) {
          retainer.add(proxy);
        }

        idsToProxy.set(id, proxy);
        return proxy;
      }

      if (isBasicObject(value)) {
        return Object.keys(value).reduce((object, key) => ({ ...object,
          [key]: decode(value[key], retainedBy)
        }), {});
      }
    }

    return value;
  }
}

export { createBasicEncoder };
