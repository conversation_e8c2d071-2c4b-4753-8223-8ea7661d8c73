// Type guard for CrmPropertiesResponse
function isCrmPropertiesResponse(data) {
    if (
    // Confirm the data is a defined object
    data === null ||
        typeof data !== 'object' ||
        // Confirm all keys and values are strings, or null
        !Object.keys(data).every((key) => typeof key === 'string' &&
            (typeof data[key] === 'string' || data[key] === null))) {
        return false;
    }
    return true;
}
export const fetchCrmProperties = async (propertyNames, propertiesUpdatedCallback, options) => {
    let response;
    let result;
    try {
        // eslint-disable-next-line hubspot-dev/no-confusing-browser-globals
        response = await self.fetchCrmProperties(propertyNames, propertiesUpdatedCallback, options);
        result = await response.json();
    }
    catch (error) {
        // Only handle network/parsing errors, not our validation errors
        throw error instanceof Error
            ? error
            : new Error('Failed to fetch CRM properties: Unknown error');
    }
    if (result.error) {
        throw new Error(result.error);
    }
    if (!response.ok) {
        throw new Error(`Failed to fetch CRM properties: ${response.statusText}`);
    }
    if (!isCrmPropertiesResponse(result.data)) {
        throw new Error('Invalid response format');
    }
    return result;
};
