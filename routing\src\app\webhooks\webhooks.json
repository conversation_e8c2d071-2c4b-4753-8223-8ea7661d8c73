{"settings": {"targetUrl": "http://localhost:3000/webhook", "maxConcurrentRequests": 10}, "subscriptions": {"crmObjects": [{"subscriptionType": "object.propertyChange", "objectName": "deal", "propertyName": "route_search", "active": true}, {"subscriptionType": "object.creation", "objectName": "deal", "active": true}, {"subscriptionType": "object.propertyChange", "objectName": "contact", "propertyName": "firstname", "active": false}, {"subscriptionType": "object.creation", "objectName": "contact", "active": false}], "legacyCrmObjects": [{"subscriptionType": "contact.propertyChange", "propertyName": "lastname", "active": false}, {"subscriptionType": "contact.deletion", "active": false}], "hubEvents": [{"subscriptionType": "contact.privacyDeletion", "active": false}]}}