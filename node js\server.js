const express = require("express");
const axios = require("axios");
const cors = require("cors");
require("dotenv").config();

const app = express();

// Enhanced CORS configuration for HubSpot
app.use(cors({
    origin: [
        'http://localhost:3000',
        'https://app.hubspot.com',
        'https://app-eu1.hubspot.com',
        'https://app.hubspot.com',
        /\.hubspot\.com$/
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const { CLIENT_ID, CLIENT_SECRET, REDIRECT_URI ,PORT } = process.env;

let ACCESS_TOKEN = "";

/** 1️⃣ Step 1: Install URL */
app.get("/", (req, res) => {
    const authUrl = `https://app.hubspot.com/oauth/authorize?client_id=${CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=crm.objects.deals.read crm.objects.deals.write crm.schemas.deals.write crm.objects.contacts.read crm.objects.contacts.write crm.schemas.contacts.read`;
    res.send(`<a href="${authUrl}">Install HubSpot App</a>`);
});

/** 2️⃣ Step 2: OAuth Callback */
app.get("/oauth-callback", async (req, res) => {
    const { code } = req.query;

    try {
        const response = await axios.post(
            "https://api.hubapi.com/oauth/v1/token",
            new URLSearchParams({
                grant_type: "authorization_code",
                client_id: CLIENT_ID,
                client_secret: CLIENT_SECRET,
                redirect_uri: REDIRECT_URI,
                code,
            }),
            { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
        );

        ACCESS_TOKEN = response.data.access_token;
        res.send("✅ App connected to HubSpot! You can now call APIs.");
    } catch (err) {
        console.error(err.response.data);
        res.send("❌ Error during OAuth");
    }
});

/** 3️⃣ Create Custom Deal Properties */
app.post("/create-deal-properties", async (req, res) => {
    try {
        // Create all custom properties for deals
        await createMarginProperty();
        await createModeProperty();
        await createTariffProperty();
        await createPriceProperty();
        await createRouteSearchProperty();
        await createRouteDropdownProperty();
        await createTextAreaProperty();

        res.json({ message: "All custom deal properties created successfully." });
    } catch (error) {
        console.error(error.response?.data || error);
        res.status(500).send("Error creating deal properties");
    }
});

/** 4️⃣ Update Deal Properties */
app.post("/update-deal/:dealId", async (req, res) => {
    const { dealId } = req.params;
    const { properties } = req.body;

    console.log(`Updating deal ${dealId} with properties:`, properties);
    console.log(`Using access token: ${ACCESS_TOKEN ? 'Available' : 'Missing'}`);

    try {
        if (!ACCESS_TOKEN) {
            return res.status(401).json({ error: "No access token available. Please authenticate first." });
        }

        // Update the deal with new properties
        const response = await axios.patch(
            `https://api.hubapi.com/crm/v3/objects/deals/${dealId}`,
            { properties },
            {
                headers: {
                    Authorization: `Bearer ${ACCESS_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log("Deal updated successfully:", response.data);
        res.json({ message: "Deal updated successfully.", data: response.data });
    } catch (error) {
        console.error("Error updating deal:", error.response?.data || error.message);
        const errorMessage = error.response?.data?.message || error.message || "Unknown error";
        res.status(error.response?.status || 500).json({
            error: "Error updating deal",
            details: errorMessage,
            dealId: dealId,
            properties: properties
        });
    }
});

/** 5️⃣ Webhook Handler */
app.post("/webhook", (req, res) => {
    const events = req.body;
    console.log("Webhook received:", JSON.stringify(events, null, 2));

    // Process webhook events
    if (Array.isArray(events)) {
        events.forEach(event => {
            if (event.subscriptionType === "object.propertyChange" &&
                event.objectName === "deal" &&
                event.propertyName === "route_search") {
                console.log(`Deal ${event.objectId} route_search changed to: ${event.propertyValue}`);

                // Auto-create route properties if route_search is set to "yes"
                if (event.propertyValue === "yes") {
                    handleRouteSearchEnabled(event.objectId);
                }
            }
        });
    }

    res.status(200).send("OK");
});

/** 6️⃣ Handle Route Search Enabled */
async function handleRouteSearchEnabled(dealId) {
    try {
        console.log(`Auto-processing deal ${dealId} with route search enabled`);
        // You can add automatic processing logic here
        // For example, automatically create route selector options
    } catch (error) {
        console.error("Error handling route search enabled:", error);
    }
}

/** 7️⃣ Polling Example: Check Deals with route_search = Yes */
app.get("/check-deals", async (req, res) => {
    try {
        const deals = await axios.get("https://api.hubapi.com/crm/v3/objects/deals", {
            headers: { Authorization: `Bearer ${ACCESS_TOKEN}` },
            params: { properties: "route_search,route_selector,selected_route" },
        });

        const filtered = deals.data.results.filter(
            (d) => d.properties.route_search === "yes"
        );

        res.json({
            message: "Deals with route search enabled",
            count: filtered.length,
            deals: filtered
        });
    } catch (error) {
        console.error(error.response?.data || error);
        res.status(500).send("Error checking deals");
    }
});


// Property creation functions
async function createMarginProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "margin",
                label: "Margin",
                type: "string",
                fieldType: "text",
                description: "Deal margin value"
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) { // Ignore if property already exists
            throw error;
        }
    }
}

async function createModeProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "mode",
                label: "Mode",
                type: "string",
                fieldType: "text",
                description: "Deal mode"
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

async function createTariffProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "tariff",
                label: "Tariff",
                type: "string",
                fieldType: "text",
                description: "Deal tariff"
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

async function createPriceProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "price",
                label: "Price",
                type: "string",
                fieldType: "text",
                description: "Deal price"
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

async function createRouteSearchProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "route_search",
                label: "Route Search",
                type: "enumeration",
                fieldType: "select",
                description: "Enable route search functionality",
                options: [
                    { label: "Yes", value: "yes" },
                    { label: "No", value: "no" }
                ]
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

async function createRouteDropdownProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "route",
                label: "Route",
                type: "enumeration",
                fieldType: "select",
                description: "Select route option",
                options: [
                    { label: "Route 1", value: "Route 1" },
                    { label: "Route 2", value: "Route 2" },
                    { label: "Route 3", value: "Route 3" },
                    { label: "Route 4", value: "Route 4" },
                    { label: "Route 5", value: "Route 5" }
                ]
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

async function createTextAreaProperty() {
    try {
        await axios.post(
            "https://api.hubapi.com/crm/v3/properties/deals",
            {
                name: "route_textarea",
                label: "Route Textarea",
                type: "string",
                fieldType: "textarea",
                description: "Details of the selected route"
            },
            { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
        );
    } catch (error) {
        if (error.response?.status !== 409) {
            throw error;
        }
    }
}

// Legacy function for backward compatibility
async function createDropdownProperty() {
    return createRouteDropdownProperty();
}



async function updateSelectedRoute(dealId, selectedValue) {
  await axios.patch(
    `https://api.hubapi.com/crm/v3/objects/deals/${dealId}`,
    { properties: { selected_route: selectedValue } },
    { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
  );
}


app.listen(PORT, () => {
    console.log("Server is running on port 3000");
});