function isAssociationsResponse(data) {
    if (data === null ||
        typeof data !== 'object' ||
        !Array.isArray(data.results) ||
        typeof data.hasMore !== 'boolean' ||
        typeof data.nextOffset !== 'number') {
        return false;
    }
    return data.results.every((result) => result !== null &&
        typeof result === 'object' &&
        typeof result.toObjectId === 'number' &&
        Array.isArray(result.associationTypes) &&
        result.properties !== null &&
        typeof result.properties === 'object');
}
export const fetchAssociations = async (request, options) => {
    let response;
    let result;
    try {
        // eslint-disable-next-line hubspot-dev/no-confusing-browser-globals
        response = await self.fetchAssociations(request, options);
        result = await response.json();
    }
    catch (error) {
        throw error instanceof Error
            ? error
            : new Error('Failed to fetch associations: Unknown error');
    }
    if (result.error) {
        throw new Error(result.error);
    }
    if (!response.ok) {
        throw new Error(`Failed to fetch associations: ${response.statusText}`);
    }
    if (!isAssociationsResponse(result.data)) {
        throw new Error('Invalid response format');
    }
    return {
        data: result.data,
        cleanup: result.cleanup || (() => { }),
    };
};
