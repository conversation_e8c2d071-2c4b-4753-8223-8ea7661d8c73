(function(React2, react) {
  "use strict";
  function extend_V2(renderExtensionCallback) {
    return self.extend_V2(renderExtensionCallback);
  }
  function serverless(name, options) {
    return self.serverless(name, options);
  }
  function fetch(url, options) {
    return self.hsFetch(url, options);
  }
  const hubspot = {
    extend: extend_V2,
    serverless,
    fetch
  };
  react.createRemoteReactComponent("Alert");
  react.createRemoteReactComponent("Button", {
    fragmentProps: ["overlay"]
  });
  react.createRemoteReactComponent("ButtonRow");
  react.createRemoteReactComponent("Card");
  react.createRemoteReactComponent("DescriptionList");
  react.createRemoteReactComponent("DescriptionListItem");
  react.createRemoteReactComponent("Divider");
  react.createRemoteReactComponent("EmptyState");
  react.createRemoteReactComponent("ErrorState");
  react.createRemoteReactComponent("Form");
  react.createRemoteReactComponent("Heading");
  react.createRemoteReactComponent("Image", {
    fragmentProps: ["overlay"]
  });
  react.createRemoteReactComponent("Input");
  const Link = react.createRemoteReactComponent("Link", {
    fragmentProps: ["overlay"]
  });
  react.createRemoteReactComponent("TextArea");
  react.createRemoteReactComponent("Textarea");
  react.createRemoteReactComponent("LoadingSpinner");
  react.createRemoteReactComponent("ProgressBar");
  react.createRemoteReactComponent("Select");
  react.createRemoteReactComponent("Tag", {
    fragmentProps: ["overlay"]
  });
  const Text = react.createRemoteReactComponent("Text");
  react.createRemoteReactComponent("Tile");
  react.createRemoteReactComponent("Stack");
  react.createRemoteReactComponent("ToggleGroup");
  react.createRemoteReactComponent("StatisticsItem");
  react.createRemoteReactComponent("Statistics");
  react.createRemoteReactComponent("StatisticsTrend");
  react.createRemoteReactComponent("Table");
  react.createRemoteReactComponent("TableFooter");
  react.createRemoteReactComponent("TableCell");
  react.createRemoteReactComponent("TableRow");
  react.createRemoteReactComponent("TableBody");
  react.createRemoteReactComponent("TableHeader");
  react.createRemoteReactComponent("TableHead");
  react.createRemoteReactComponent("NumberInput");
  react.createRemoteReactComponent("Box");
  react.createRemoteReactComponent("StepIndicator");
  react.createRemoteReactComponent("Accordion");
  react.createRemoteReactComponent("MultiSelect");
  react.createRemoteReactComponent("Flex");
  react.createRemoteReactComponent("DateInput");
  react.createRemoteReactComponent("Checkbox");
  react.createRemoteReactComponent("RadioButton");
  const List = react.createRemoteReactComponent("List");
  react.createRemoteReactComponent("Toggle");
  react.createRemoteReactComponent("Dropdown");
  react.createRemoteReactComponent("Panel");
  react.createRemoteReactComponent("PanelFooter");
  react.createRemoteReactComponent("PanelBody");
  react.createRemoteReactComponent("PanelSection");
  react.createRemoteReactComponent("StepperInput");
  react.createRemoteReactComponent("Modal");
  react.createRemoteReactComponent("ModalBody");
  react.createRemoteReactComponent("ModalFooter");
  react.createRemoteReactComponent("Icon");
  react.createRemoteReactComponent("StatusTag");
  react.createRemoteReactComponent("LoadingButton", {
    fragmentProps: ["overlay"]
  });
  react.createRemoteReactComponent("BarChart");
  react.createRemoteReactComponent("LineChart");
  react.createRemoteReactComponent("Tabs");
  react.createRemoteReactComponent("Tab");
  react.createRemoteReactComponent("Illustration");
  react.createRemoteReactComponent("Tooltip");
  react.createRemoteReactComponent("SearchInput");
  var ServerlessExecutionStatus;
  (function(ServerlessExecutionStatus2) {
    ServerlessExecutionStatus2["Success"] = "SUCCESS";
    ServerlessExecutionStatus2["Error"] = "ERROR";
  })(ServerlessExecutionStatus || (ServerlessExecutionStatus = {}));
  hubspot.extend(() => /* @__PURE__ */ React2.createElement(Extension, null));
  const Extension = () => {
    return /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(Text, null, "Congrats! You just deployed your first App card. What's next? Here are some pointers to get you started:"), /* @__PURE__ */ React2.createElement(List, { variant: "unordered-styled" }, /* @__PURE__ */ React2.createElement(Link, { href: "https://developers.hubspot.com/docs/platform/ui-components" }, "📖 Explore our library of UI component"), /* @__PURE__ */ React2.createElement(Link, { href: "www.developers.hubspot.com" }, "📖 Learn more about utilities to help you build better extension"), /* @__PURE__ */ React2.createElement(Link, { href: "github.com/hubspot/ui-extensions-examples" }, "📖 Get inspired by private app code samples"), /* @__PURE__ */ React2.createElement(Link, { href: "https://ecosystem.hubspot.com/marketplace/apps/app-cards" }, "📖 Look at the Marketplace collection of apps that contain app cards"), /* @__PURE__ */ React2.createElement(Link, { href: "https://ecosystem.hubspot.com/marketplace/apps/app-cards" }, "▶️ Find resources to learn more"), /* @__PURE__ */ React2.createElement(Link, { href: "https://developers.hubspot.com/slack" }, "▶️ Connect with developers on #ui-extensions channel on developer Slack community")));
  };
})(React, RemoteUI);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
