export type CrmPropertiesResponse = {
    [key: string]: string | null;
};
export type FetchCrmPropertiesOptions = {
    propertiesToFormat?: string[] | 'all';
    formattingOptions?: {
        date?: {
            format?: string;
            relative?: boolean;
        };
        dateTime?: {
            format?: string;
            relative?: boolean;
        };
        currency?: {
            addSymbol?: boolean;
        };
    };
};
export interface FetchCrmPropertiesResult {
    data: Record<string, string | null>;
    error?: string;
    cleanup: () => void;
}
export declare const fetchCrmProperties: (propertyNames: string[], propertiesUpdatedCallback: (properties: Record<string, string | null>) => void, options?: FetchCrmPropertiesOptions) => Promise<FetchCrmPropertiesResult>;
