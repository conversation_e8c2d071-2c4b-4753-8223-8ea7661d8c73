import { version } from 'react';
import { createR<PERSON>onci<PERSON> } from './reconciler.mjs';
import { RenderContext } from './context.mjs';
import { jsx } from 'react/jsx-runtime';

const cache = new WeakMap(); // @see https://github.com/facebook/react/blob/fea6f8da6ab669469f2fa3f18bd3a831f00ab284/packages/react-reconciler/src/ReactRootTags.js#L12
// We don't support concurrent rendering for now.

const LEGACY_ROOT = 0;
const defaultReconciler = createReconciler();
function createRoot(root) {
  return {
    render(children) {
      render(children, root);
    },

    unmount() {
      if (!cache.has(root)) return;
      render(null, root);
      cache.delete(root);
    }

  };
}
/**
 * @deprecated Use `createRoot` for a React 18-style rendering API.
 */

function render(element, root, callback, reconciler = defaultReconciler) {
  // First, check if we've already cached a container and render context for this root
  let cached = cache.get(root);

  if (!cached) {
    var _version$split;

    const major = Number(((_version$split = version.split('.')) === null || _version$split === void 0 ? void 0 : _version$split[0]) || 18); // Since we haven't created a container for this root yet, create a new one

    const value = {
      container: major >= 18 ? reconciler.createContainer(root, LEGACY_ROOT, null, false, null, // Might not be necessary
      'r-ui', () => null, null) : // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore - this is to support React 17
      reconciler.createContainer(root, LEGACY_ROOT, false, null),
      // We also cache the render context to avoid re-creating it on subsequent render calls
      renderContext: {
        root,
        reconciler
      }
    }; // Store the container and render context for retrieval on subsequent render calls

    cache.set(root, value);
    cached = value;
  }

  const {
    container,
    renderContext
  } = cached; // callback is cast here because the typings do not mark that argument
  // as optional, even though it is.

  reconciler.updateContainer(element && /*#__PURE__*/jsx(RenderContext.Provider, {
    value: renderContext,
    children: element
  }), container, null, callback); // Did not work for me because (I think?) it is done by the worker
  // and therefore has an entirely different React.
  //
  // Original code was from:
  // @see https://github.com/facebook/react/issues/16666
  // @see https://github.com/michalochman/react-pixi-fiber/pull/148
  //
  // reconciler.injectIntoDevTools({
  //   bundleType: 1,
  //   findFiberByHostInstance: reconciler.findFiberByHostInstance,
  //   rendererPackageName: '@remote-ui/react',
  //   version: '16.9.0',
  // });
}

export { createRoot, render };
