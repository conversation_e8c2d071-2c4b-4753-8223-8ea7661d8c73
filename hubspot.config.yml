defaultPortal: routess
portals:
  - name: routess
    portalId: *********
    env: prod
    authType: personalaccesskey
    auth:
      tokenInfo:
        accessToken: >-
          CKGo7auHMxIpQlNQMl8kQEwrAhwACAkCARMXAQEBAgECAQIBDQMKAQIDBxsBAQEBQgEYr6GNdCDflpMnKO7pDDIUOoqnCJ-SfscYvNx0VgjI0w0xXFw6VkJTUDJfJEBMKwJJAAgTBgYdAQEBUSscAQEBAQ8BAQQFAQEBAQQBBQEBAQgBBgIDARAEDwEIBAEBAQEBBAEFARIJAwExAQEBBAEBAQEBAQEBQQEHWhoBQhTNXHzX1EnpwhccB2fLcSjpyMf1bkoDbmEyUgBaAGABaN-WkydwAHgA
        expiresAt: '2025-08-04T14:27:53.545Z'
    accountType: DEVELOPER_TEST
    personalAccessKey: >-
      CiRuYTItNmQ4Zi02Nzg0LTRmNWYtOTgxOC0zZDU4MDA3ZTg1NDAQr6GNdBjflpMnKhkABeaRghP-6fOFL9eSsXUi8wZfxXpgcOU4SgNuYTI
    parentAccountId: *********
  - name: kambaa
    portalId: *********
    env: prod
    authType: personalaccesskey
    auth:
      tokenInfo:
        accessToken: >-
          CNHy76uHMxISQlNQMl8kQEwrAgUACAkCeSgCGJqfjXQg35aTJyju6QwyFBmz9p6HVSA7CBqBZSdQrA6zTdoYOhZCU1AyXyRATCsCCQAIEwYGnPABbgFCQhRtk9rR6Lg7vPGR-xzL0PeBaSBSJ0oDbmEyUgBaAGABaN-WkydwAHgA
        expiresAt: '2025-08-04T14:28:35.834Z'
    accountType: APP_DEVELOPER
    personalAccessKey: >-
      CiRuYTItNDVjNC1kY2VlLTRiZTgtOTkyNi0zY2NkNmQ0NDFlMDEQmp-NdBjflpMnKhkABeaRgng3zAt0LOVgSLiQCfp7FrMKuINgSgNuYTI
