{"name": "Get started with public apps", "uid": "get-started-public-app", "description": "An example to demonstrate how to build a public app with developer projects.", "allowedUrls": ["https://api.example.com"], "auth": {"redirectUrls": ["https://your-app-domain.com/oauth-callback", "http://localhost:3000/oauth-callback"], "requiredScopes": ["crm.objects.deals.read", "crm.objects.deals.write", "crm.schemas.deals.write", "crm.objects.contacts.read", "crm.objects.contacts.write", "crm.schemas.contacts.read"], "optionalScopes": [], "conditionallyRequiredScopes": []}, "support": {"supportEmail": "<EMAIL>", "documentationUrl": "https://example.com/docs", "supportUrl": "https://example.com/support", "supportPhone": "+18005555555"}, "extensions": {"crm": {"cards": [{"file": "./extensions/example-card.json"}]}}}